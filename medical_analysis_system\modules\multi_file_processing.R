# 医学数据分析系统 - 多文件处理模块
# Medical Data Analysis System - Multi-file Processing Module

# 多文件合并函数
merge_medical_files <- function(file_list, merge_key = "icustay_id", 
                                merge_type = "inner", remove_duplicates = TRUE,
                                add_source_info = TRUE) {
  tryCatch({
    # 详细的参数验证和调试信息
    log_info("开始参数验证...")

    # 验证file_list参数
    if (is.null(file_list)) {
      stop("file_list参数为NULL")
    }
    if (length(file_list) < 2) {
      stop(paste("至少需要2个文件进行合并，当前文件数:", length(file_list)))
    }
    log_info(paste("文件列表验证通过，文件数:", length(file_list)))

    # 验证merge_key参数 - 增强的验证和默认值处理
    log_info(paste("merge_key参数:", merge_key, "类型:", class(merge_key), "长度:", length(merge_key)))
    
    # 如果merge_key为空或无效，使用默认值
    if (is.null(merge_key) || length(merge_key) == 0 || merge_key == "") {
      merge_key <- "icustay_id"
      log_warn("merge_key参数无效，使用默认值: icustay_id")
    }
    
    # 确保merge_key是单个值
    if (length(merge_key) != 1) {
      if (length(merge_key) > 1) {
        merge_key <- merge_key[1]  # 取第一个值
        log_warn(paste("merge_key长度大于1，使用第一个值:", merge_key))
      } else {
        merge_key <- "icustay_id"  # 使用默认值
        log_warn("merge_key长度为零，使用默认值: icustay_id")
      }
    }
    
    log_info(paste("merge_key验证通过:", merge_key))

    # 验证merge_type参数 - 增强的验证和默认值处理
    log_info(paste("merge_type参数:", merge_type, "类型:", class(merge_type), "长度:", length(merge_type)))
    
    # 如果merge_type为空或无效，使用默认值
    if (is.null(merge_type) || length(merge_type) == 0 || merge_type == "") {
      merge_type <- "inner"
      log_warn("merge_type参数无效，使用默认值: inner")
    }
    
    # 确保merge_type是单个值
    if (length(merge_type) != 1) {
      if (length(merge_type) > 1) {
        merge_type <- merge_type[1]  # 取第一个值
        log_warn(paste("merge_type长度大于1，使用第一个值:", merge_type))
      } else {
        merge_type <- "inner"  # 使用默认值
        log_warn("merge_type长度为零，使用默认值: inner")
      }
    }
    
    # 验证merge_type的值是否有效
    if (!merge_type %in% c("inner", "left", "outer")) {
      merge_type <- "inner"  # 使用默认值
      log_warn(paste("merge_type值无效，使用默认值: inner"))
    }
    
    log_info(paste("merge_type验证通过:", merge_type))

    log_info(paste("开始合并", length(file_list), "个文件"))
    
    # 检查所有文件是否都包含合并键
    missing_key_files <- c()
    for (i in seq_along(file_list)) {
      # 确保merge_key是单个值，并检查是否存在
      if (length(merge_key) != 1 || (length(merge_key) == 1 && !merge_key %in% names(file_list[[i]]$data))) {
        missing_key_files <- c(missing_key_files, file_list[[i]]$name)
      }
    }
    
    if (length(missing_key_files) > 0) {
      stop(paste("以下文件缺少合并键", merge_key, ":", 
                paste(missing_key_files, collapse = ", ")))
    }
    
    # 开始合并过程
    merged_data <- file_list[[1]]$data
    merge_report <- list()
    merge_report$files_info <- list()
    
    # 记录第一个文件信息
    merge_report$files_info[[1]] <- list(
      name = file_list[[1]]$name,
      description = file_list[[1]]$description,
      rows = nrow(file_list[[1]]$data),
      cols = ncol(file_list[[1]]$data),
      variables = names(file_list[[1]]$data)
    )
    
    # 如果需要添加来源信息，为第一个文件添加来源列
    if (add_source_info) {
      source_col_name <- paste0("source_", gsub("[^A-Za-z0-9]", "_", file_list[[1]]$name))
      merged_data[[source_col_name]] <- 1
    }
    
    # 逐个合并其他文件
    for (i in 2:length(file_list)) {
      current_file <- file_list[[i]]
      current_data <- current_file$data
      
      log_info(paste("合并文件", i, ":", current_file$name))
      
      # 记录当前文件信息
      merge_report$files_info[[i]] <- list(
        name = current_file$name,
        description = current_file$description,
        rows = nrow(current_data),
        cols = ncol(current_data),
        variables = names(current_data)
      )
      
      # 如果需要添加来源信息
      if (add_source_info) {
        source_col_name <- paste0("source_", gsub("[^A-Za-z0-9]", "_", current_file$name))
        current_data[[source_col_name]] <- 1
      }
      
      # 检查重复列名（除了合并键）
      common_cols <- intersect(names(merged_data), names(current_data))
      duplicate_cols <- setdiff(common_cols, merge_key)
      
      if (length(duplicate_cols) > 0 & remove_duplicates) {
        log_warn(paste("发现重复列:", paste(duplicate_cols, collapse = ", "),
                      "将从文件", current_file$name, "中移除"))
        # 安全地移除重复列
        keep_cols <- !names(current_data) %in% duplicate_cols
        current_data <- current_data[, keep_cols, drop = FALSE]
      } else if (length(duplicate_cols) > 0) {
        # 重命名重复列
        for (col in duplicate_cols) {
          new_name <- paste0(col, "_", gsub("[^A-Za-z0-9]", "_", current_file$name))
          # 安全地重命名列
          col_indices <- which(names(current_data) == col)
          names(current_data)[col_indices] <- new_name
          log_info(paste("重命名重复列:", col, "->", new_name))
        }
      }
      
      # 执行合并
      if (merge_type == "inner") {
        merged_data <- merge(merged_data, current_data)
      } else if (merge_type == "left") {
        merged_data <- merge(merged_data, current_data, by = merge_key, all.x = TRUE)
      } else if (merge_type == "outer") {
        merged_data <- merge(merged_data, current_data, by = merge_key, all = TRUE)
      }
      
      log_info(paste("合并后数据维度:", nrow(merged_data), "行,", ncol(merged_data), "列"))
    }
    
    # 生成合并报告
    merge_report$summary <- list(
      total_files = length(file_list),
      merge_key = merge_key,
      merge_type = merge_type,
      final_rows = nrow(merged_data),
      final_cols = ncol(merged_data),
      removed_duplicates = remove_duplicates,
      added_source_info = add_source_info
    )
    
    # 计算合并统计
    merge_report$statistics <- calculate_merge_statistics(file_list, merged_data, merge_key)
    
    log_info("文件合并完成")
    
    return(list(
      data = merged_data,
      report = merge_report
    ))
    
  }, error = function(e) {
    log_error(paste("文件合并失败:", e$message))
    stop(paste("文件合并失败:", e$message))
  })
}

# 计算合并统计信息
calculate_merge_statistics <- function(file_list, merged_data, merge_key) {
  tryCatch({
    stats <- list()
    
    # 计算每个文件的唯一ID数量
    unique_ids_by_file <- sapply(file_list, function(f) {
      length(unique(f$data[[merge_key]]))
    })
    
    # 计算合并后的唯一ID数量
    final_unique_ids <- length(unique(merged_data[[merge_key]]))
    
    stats$unique_ids <- list(
      by_file = unique_ids_by_file,
      final = final_unique_ids,
      total_original = sum(unique_ids_by_file)
    )
    
    # 计算数据覆盖率
    coverage <- final_unique_ids / max(unique_ids_by_file) * 100
    stats$coverage_rate <- round(coverage, 2)
    
    # 计算缺失值统计
    missing_stats <- sapply(merged_data, function(x) sum(is.na(x)))
    stats$missing_values <- list(
      total = sum(missing_stats),
      by_variable = missing_stats,
      percentage = round(sum(missing_stats) / (nrow(merged_data) * ncol(merged_data)) * 100, 2)
    )
    
    return(stats)
    
  }, error = function(e) {
    log_error(paste("统计计算失败:", e$message))
    return(list())
  })
}

# 验证文件兼容性
validate_file_compatibility <- function(file_list, merge_key = "icustay_id") {
  tryCatch({
    validation_results <- list()
    
    # 检查合并键存在性
    missing_key <- sapply(file_list, function(f) {
      !merge_key %in% names(f$data)
    })
    
    validation_results$missing_merge_key <- names(file_list)[missing_key]
    
    # 检查合并键数据类型一致性
    key_types <- sapply(file_list, function(f) {
      if (merge_key %in% names(f$data)) {
        class(f$data[[merge_key]])[1]
      } else {
        NA
      }
    })
    
    validation_results$key_type_consistency <- length(unique(key_types[!is.na(key_types)])) == 1
    validation_results$key_types <- key_types
    
    # 检查ID重叠情况
    all_ids <- lapply(file_list, function(f) {
      if (merge_key %in% names(f$data)) {
        unique(f$data[[merge_key]])
      } else {
        c()
      }
    })
    
    # 计算两两重叠
    overlap_matrix <- matrix(0, nrow = length(file_list), ncol = length(file_list))
    for (i in 1:length(file_list)) {
      for (j in 1:length(file_list)) {
        if (i != j) {
          overlap_matrix[i, j] <- length(intersect(all_ids[[i]], all_ids[[j]]))
        }
      }
    }
    
    validation_results$id_overlaps <- overlap_matrix
    validation_results$total_unique_ids <- length(unique(unlist(all_ids)))
    
    # 生成兼容性评分
    score <- 100
    if (length(validation_results$missing_merge_key) > 0) score <- score - 50
    if (!validation_results$key_type_consistency) score <- score - 30
    if (validation_results$total_unique_ids == 0) score <- 0
    
    validation_results$compatibility_score <- max(0, score)
    
    return(validation_results)
    
  }, error = function(e) {
    log_error(paste("文件兼容性验证失败:", e$message))
    return(list(compatibility_score = 0, error = e$message))
  })
}

# 生成合并预览
generate_merge_preview <- function(file_list, merge_key = "icustay_id", 
                                  merge_type = "inner", sample_size = 100) {
  tryCatch({
    if (length(file_list) < 2) {
      return(NULL)
    }
    
    # 对每个文件进行采样以加快预览速度
    sampled_files <- lapply(file_list, function(f) {
      if (nrow(f$data) > sample_size) {
        sample_indices <- sample(nrow(f$data), sample_size)
        f$data <- f$data[sample_indices, ]
      }
      return(f)
    })
    
    # 执行小规模合并
    preview_result <- merge_medical_files(
      sampled_files, 
      merge_key = merge_key,
      merge_type = merge_type,
      remove_duplicates = TRUE,
      add_source_info = FALSE
    )
    
    return(preview_result)
    
  }, error = function(e) {
    log_error(paste("合并预览生成失败:", e$message))
    return(NULL)
  })
}

# 导出合并数据
export_merged_data <- function(merged_data, filename = NULL, format = "csv") {
  tryCatch({
    if (is.null(filename)) {
      filename <- paste0("merged_medical_data_", Sys.Date(), ".", format)
    }

    if (format == "csv") {
      write.csv(merged_data, filename, row.names = FALSE, fileEncoding = "UTF-8")
    } else if (format == "xlsx") {
      library(openxlsx)
      write.xlsx(merged_data, filename)
    }

    log_info(paste("合并数据已导出:", filename))
    return(filename)

  }, error = function(e) {
    log_error(paste("数据导出失败:", e$message))
    stop(paste("数据导出失败:", e$message))
  })
}

# 自动检测分隔符
detect_separator <- function(file_path, user_choice = "auto") {
  tryCatch({
    if (user_choice != "auto") {
      return(user_choice)
    }

    # 读取文件前几行进行检测
    sample_lines <- readLines(file_path, n = 5, warn = FALSE)
    if (length(sample_lines) == 0) {
      return(",")  # 默认返回逗号
    }

    # 测试不同分隔符
    separators <- c(",", "\t", ";", "|", " ")
    separator_scores <- sapply(separators, function(sep) {
      tryCatch({
        # 尝试用该分隔符分割第一行
        first_line_parts <- strsplit(sample_lines[1], sep, fixed = TRUE)[[1]]

        # 检查其他行是否有相同数量的列
        consistent_cols <- sapply(sample_lines[-1], function(line) {
          parts <- strsplit(line, sep, fixed = TRUE)[[1]]
          length(parts) == length(first_line_parts)
        })

        # 计算一致性分数
        consistency_score <- sum(consistent_cols) / length(consistent_cols)

        # 列数合理性分数（2-50列比较合理）
        col_count <- length(first_line_parts)
        col_score <- if (col_count >= 2 & col_count <= 50) 1 else 0.5

        return(consistency_score * col_score * col_count)

      }, error = function(e) {
        return(0)
      })
    })

    # 选择得分最高的分隔符
    best_separator <- separators[which.max(separator_scores)]

    log_info(paste("自动检测分隔符:", best_separator))
    return(best_separator)

  }, error = function(e) {
    log_warn(paste("分隔符检测失败，使用默认逗号:", e$message))
    return(",")
  })
}

# 自动生成文件描述
generate_auto_description <- function(filename, data) {
  tryCatch({
    # 基于文件名生成描述
    filename_lower <- tolower(filename)

    # 常见的医学数据文件名模式
    if (grepl("basic|patient|demo|info", filename_lower)) {
      return("患者基本信息")
    } else if (grepl("lab|laboratory|血|检验", filename_lower)) {
      return("实验室检查")
    } else if (grepl("vital|生命体征|体征", filename_lower)) {
      return("生命体征")
    } else if (grepl("outcome|结局|死亡|mortality", filename_lower)) {
      return("临床结局")
    } else if (grepl("drug|medication|药物|用药", filename_lower)) {
      return("药物信息")
    } else if (grepl("diagnosis|诊断|icd", filename_lower)) {
      return("诊断信息")
    } else if (grepl("procedure|操作|手术", filename_lower)) {
      return("医疗操作")
    } else if (grepl("chart|记录|note", filename_lower)) {
      return("病历记录")
    } else {
      # 基于数据内容生成描述
      col_names <- tolower(names(data))

      if (any(grepl("hemoglobin|hgb|血红蛋白", col_names))) {
        return("血液检查")
      } else if (any(grepl("heart_rate|hr|心率", col_names))) {
        return("生命体征监测")
      } else if (any(grepl("creatinine|肌酐|尿素", col_names))) {
        return("肾功能检查")
      } else if (any(grepl("glucose|血糖|glc", col_names))) {
        return("血糖监测")
      } else {
        # 默认描述
        return(paste("数据文件 (", ncol(data), "列)"))
      }
    }

  }, error = function(e) {
    log_warn(paste("自动描述生成失败:", e$message))
    return("数据文件")
  })
}
