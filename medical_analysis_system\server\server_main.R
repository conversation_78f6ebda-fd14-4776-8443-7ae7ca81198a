# 医学数据分析系统 - 主服务器逻辑
# Medical Data Analysis System - Main Server Logic

server <- function(input, output, session) {
  
  # 统一的响应式数据存储
  values <- reactiveValues(
    # 主数据存储
    raw_data = NULL,
    processed_data = NULL,
    analysis_results = list(),
    current_analysis_id = NULL,

    # 数据来源信息
    data_source = "none",  # "single_file", "multi_file", "none"
    data_info = list(),

    # 多文件相关数据
    multi_files = list(),
    file_counter = 1,
    merged_data = NULL,
    merge_report = NULL,
    cleaned_data = NULL,
    cleaning_report = NULL
  )
  
  # 仪表板相关输出
  output$data_status_box <- renderValueBox({
    valueBox(
      value = if (is.null(values$raw_data)) "未上传" else "已上传",
      subtitle = "数据状态",
      icon = icon("database"),
      color = if (is.null(values$raw_data)) "red" else "green"
    )
  })
  
  output$analysis_tasks_box <- renderValueBox({
    valueBox(
      value = length(values$analysis_results),
      subtitle = "分析任务",
      icon = icon("tasks"),
      color = "blue"
    )
  })
  
  output$reports_count_box <- renderValueBox({
    valueBox(
      value = "0",
      subtitle = "生成报告",
      icon = icon("file-alt"),
      color = "yellow"
    )
  })
  
  output$system_uptime_box <- renderValueBox({
    valueBox(
      value = format(Sys.time(), "%H:%M"),
      subtitle = "系统时间",
      icon = icon("clock"),
      color = "purple"
    )
  })
  
  # 数据上传处理
  observeEvent(input$data_file, {
    req(input$data_file)
    
    tryCatch({
      # 显示加载提示
      showNotification("正在上传数据...", type = "message", duration = NULL, id = "upload_msg")
      
      # 读取数据
      file_path <- input$data_file$datapath
      
      values$raw_data <- read_medical_data(
        file_path,
        separator = input$file_separator,
        encoding = input$file_encoding,
        header = input$file_header,
        stringsAsFactors = input$file_stringsAsFactors
      )
      
      # 自动类型检测和转换
      values$raw_data <- detect_and_convert_types(values$raw_data)
      
      # 移除加载提示
      removeNotification("upload_msg")
      
      # 显示成功消息
      show_success(paste("数据上传成功！", nrow(values$raw_data), "行,", 
                        ncol(values$raw_data), "列"))
      
      # 更新变量选择列表
      updateSelectInput(session, "desc_group_var", 
                       choices = c("无" = "", get_variable_choices(values$raw_data)))
      
      updateCheckboxGroupInput(session, "desc_variables",
                              choices = get_variable_choices(values$raw_data))
      
      # 更新分析界面的变量选择
      binary_vars <- names(values$raw_data)[sapply(values$raw_data, function(x) {
        length(unique(x[!is.na(x)])) == 2
      })]
      
      updateSelectInput(session, "uni_outcome_var", choices = binary_vars)
      updateSelectInput(session, "multi_outcome_var", choices = binary_vars)
      updateSelectInput(session, "lasso_outcome_var", choices = binary_vars)
      
      all_vars <- get_variable_choices(values$raw_data)
      updateCheckboxGroupInput(session, "uni_covariates", choices = all_vars)
      updateCheckboxGroupInput(session, "multi_covariates", choices = all_vars)
      updateCheckboxGroupInput(session, "lasso_covariates", choices = all_vars)
      
    }, error = function(e) {
      removeNotification("upload_msg")
      show_warning(paste("数据上传失败:", e$message))
    })
  })
  
  # 数据预览
  output$data_table <- DT::renderDataTable({
    req(values$raw_data)
    
    DT::datatable(
      values$raw_data,
      options = list(
        scrollX = TRUE,
        pageLength = 10,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      filter = "top",
      rownames = FALSE
    )
  })
  
  # 变量信息表
  output$variable_info <- DT::renderDataTable({
    req(values$raw_data)
    
    var_info <- create_variable_info_table(values$raw_data)
    
    DT::datatable(
      var_info,
      options = list(
        pageLength = 15,
        dom = 't',
        ordering = FALSE
      ),
      class = "display compact",
      rownames = FALSE
    )
  })
  
  # 数据概览值框
  output$preview_rows <- renderValueBox({
    valueBox(
      value = if (is.null(values$raw_data)) 0 else nrow(values$raw_data),
      subtitle = "数据行数",
      icon = icon("list"),
      color = "blue"
    )
  })
  
  output$preview_cols <- renderValueBox({
    valueBox(
      value = if (is.null(values$raw_data)) 0 else ncol(values$raw_data),
      subtitle = "变量数量",
      icon = icon("columns"),
      color = "green"
    )
  })
  
  output$preview_missing <- renderValueBox({
    missing_count <- if (is.null(values$raw_data)) 0 else sum(is.na(values$raw_data))
    valueBox(
      value = missing_count,
      subtitle = "缺失值",
      icon = icon("question-circle"),
      color = if (missing_count > 0) "yellow" else "green"
    )
  })
  
  output$preview_numeric <- renderValueBox({
    numeric_count <- if (is.null(values$raw_data)) 0 else sum(sapply(values$raw_data, is.numeric))
    valueBox(
      value = numeric_count,
      subtitle = "数值变量",
      icon = icon("calculator"),
      color = "purple"
    )
  })
  
  # 缺失值模式图
  output$missing_plot <- renderPlot({
    req(values$raw_data)
    
    tryCatch({
      create_missing_pattern_plot(values$raw_data)
    }, error = function(e) {
      plot(1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, "无法生成缺失值图表", cex = 1.5, col = "red")
    })
  })
  
  # 相关性热图
  output$correlation_plot <- renderPlot({
    req(values$raw_data)
    
    tryCatch({
      create_correlation_heatmap(values$raw_data)
    }, error = function(e) {
      plot(1, type = "n", axes = FALSE, xlab = "", ylab = "")
      text(1, 1, "无法生成相关性图表", cex = 1.5, col = "red")
    })
  })
  
  # 数据清洗
  observeEvent(input$clean_data, {
    req(values$raw_data)
    
    tryCatch({
      showNotification("正在清洗数据...", type = "message", duration = NULL, id = "clean_msg")
      
      # 准备清洗选项
      clean_options <- list(
        missing_method = input$missing_method,
        mice_iterations = input$mice_iterations,
        normalize = input$normalize_numeric,
        log_transform = input$log_transform,
        remove_outliers = input$remove_outliers,
        outlier_threshold = input$outlier_threshold
      )
      
      # 执行数据清洗
      values$processed_data <- preprocess_medical_data(values$raw_data, clean_options)
      
      removeNotification("clean_msg")
      show_success("数据清洗完成！")
      
      # 显示清洗结果
      shinyjs::show("cleaning_results")
      shinyjs::hide("cleaning_status")
      
    }, error = function(e) {
      removeNotification("clean_msg")
      show_warning(paste("数据清洗失败:", e$message))
    })
  })
  
  # 描述性统计分析
  observeEvent(input$run_descriptive, {
    req(values$raw_data)
    
    tryCatch({
      showNotification("正在进行描述性统计分析...", type = "message", duration = NULL, id = "desc_msg")
      
      group_var <- if (input$desc_group_var == "") NULL else input$desc_group_var
      
      desc_results <- perform_descriptive_analysis(values$raw_data, group_var)
      values$analysis_results$descriptive <- desc_results
      
      removeNotification("desc_msg")
      show_success("描述性统计分析完成！")
      
    }, error = function(e) {
      removeNotification("desc_msg")
      show_warning(paste("描述性统计分析失败:", e$message))
    })
  })
  
  # 输出描述性统计表格
  output$descriptive_table <- DT::renderDataTable({
    req(values$analysis_results$descriptive)
    
    DT::datatable(
      values$analysis_results$descriptive$table_one_df,
      options = list(
        scrollX = TRUE,
        pageLength = 20,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      rownames = TRUE
    )
  })
  
  # 单因素分析
  observeEvent(input$run_univariate, {
    req(values$raw_data, input$uni_outcome_var, input$uni_covariates)
    
    # 验证参数
    validation <- validate_analysis_params(values$raw_data, input$uni_outcome_var, input$uni_covariates)
    if (!validation$valid) {
      show_warning(paste("参数验证失败:", paste(validation$errors, collapse = "; ")))
      return()
    }
    
    tryCatch({
      showNotification("正在进行单因素分析...", type = "message", duration = NULL, id = "uni_msg")
      
      uni_results <- perform_univariate_analysis(
        values$raw_data, 
        input$uni_outcome_var, 
        input$uni_covariates
      )
      
      values$analysis_results$univariate <- uni_results
      
      removeNotification("uni_msg")
      show_success("单因素分析完成！")
      
    }, error = function(e) {
      removeNotification("uni_msg")
      show_warning(paste("单因素分析失败:", e$message))
    })
  })
  
  # 输出单因素分析表格
  output$univariate_table <- DT::renderDataTable({
    req(values$analysis_results$univariate)
    
    formatted_results <- format_results_table(values$analysis_results$univariate)
    
    DT::datatable(
      formatted_results,
      options = list(
        scrollX = TRUE,
        pageLength = 15,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      rownames = FALSE
    )
  })
  
  # 下载示例数据
  output$download_sample <- downloadHandler(
    filename = function() {
      paste0("sample_medical_data_", Sys.Date(), ".csv")
    },
    content = function(file) {
      sample_data <- generate_sample_data(500)
      write.csv(sample_data, file, row.names = FALSE, fileEncoding = "UTF-8")
    }
  )
  
  # 快速开始按钮
  observeEvent(input$start_analysis, {
    updateTabItems(session, "sidebar_menu", "data_upload")
    show_success("请先上传您的数据文件开始分析")
  })

  # ==================== 多文件上传功能 ====================
  # 注意：多文件数据现在存储在统一的values对象中

  # 更新合并键选择框的函数
  update_merge_key_choices <- function() {
    if (length(values$multi_files) > 0) {
      # 获取所有文件的共同列名
      all_columns <- lapply(values$multi_files, function(f) names(f$data))
      common_columns <- Reduce(intersect, all_columns)

      if (length(common_columns) > 0) {
        # 创建选择列表，优先显示常见的ID列
        priority_columns <- c("icustay_id", "patient_id", "subject_id", "hadm_id", "id", "ID")
        priority_found <- intersect(priority_columns, common_columns)
        other_columns <- setdiff(common_columns, priority_found)

        # 按优先级排序
        ordered_columns <- c(priority_found, sort(other_columns))
        choices <- setNames(ordered_columns, ordered_columns)

        # 选择默认值
        default_key <- if (length(priority_found) > 0) priority_found[1] else ordered_columns[1]

        updateSelectInput(session, "merge_key",
                         choices = choices,
                         selected = default_key)

        cat("更新合并键选择框，共同列:", paste(common_columns, collapse = ", "), "\n")
        cat("默认选择:", default_key, "\n")
      } else {
        # 没有共同列，显示警告并禁用合并功能
        updateSelectInput(session, "merge_key",
                         choices = c("无共同列" = "icustay_id"),
                         selected = "icustay_id")
        show_warning("上传的文件没有共同的列名，无法进行合并")
      }
    } else {
      # 没有文件时，设置默认值
      updateSelectInput(session, "merge_key",
                       choices = c("icustay_id" = "icustay_id"),
                       selected = "icustay_id")
    }
  }

  # 批量文件上传处理
  observeEvent(input$multi_files_batch, {
    req(input$multi_files_batch)

    tryCatch({
      showNotification("正在处理上传的文件...", type = "message", duration = NULL, id = "batch_upload_msg")

      files_info <- input$multi_files_batch

      # 清空现有文件
      values$multi_files <- list()
      values$file_counter <- 0

      # 处理每个上传的文件
      for (i in 1:nrow(files_info)) {
        file_info <- files_info[i, ]

        tryCatch({
          # 检测分隔符
          separator <- detect_separator(file_info$datapath, input$batch_separator)

          # 读取文件
          data <- read_medical_data(
            file_info$datapath,
            separator = separator,
            encoding = input$batch_encoding,
            header = input$batch_header,
            stringsAsFactors = FALSE
          )

          # 生成文件描述
          file_desc <- if (input$batch_auto_desc) {
            generate_auto_description(file_info$name, data)
          } else {
            paste("文件", i)
          }

          # 存储文件信息
          file_key <- paste0("batch_file_", i)
          values$multi_files[[file_key]] <- list(
            name = file_info$name,
            description = file_desc,
            data = data,
            upload_time = Sys.time(),
            separator = separator,
            encoding = input$batch_encoding
          )

          values$file_counter <- values$file_counter + 1

        }, error = function(e) {
          show_warning(paste("文件", file_info$name, "处理失败:", e$message))
        })
      }

      removeNotification("batch_upload_msg")
      show_success(paste("批量上传完成！成功处理", length(values$multi_files), "个文件"))

      # 更新合并键选择框
      update_merge_key_choices()

    }, error = function(e) {
      removeNotification("batch_upload_msg")
      show_warning(paste("批量上传失败:", e$message))
    })
  })

  # 添加单个文件输入
  observeEvent(input$add_single_file, {
    values$file_counter <- values$file_counter + 1
    file_id <- values$file_counter

    insertUI(
      selector = "#single_files_container",
      where = "beforeEnd",
      ui = tags$div(
        id = paste0("single_file_input_", file_id),
        class = "single-file-input",
        style = "border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 15px; background: #f9f9f9;",

        tags$div(
          class = "row",
          tags$div(
            class = "col-md-4",
            fileInput(
              paste0("single_file_", file_id),
              paste("文件", file_id),
              accept = c(".csv", ".txt", ".tsv"),
              width = "100%"
            )
          ),
          tags$div(
            class = "col-md-4",
            textInput(
              paste0("single_desc_", file_id),
              "文件描述",
              placeholder = "请输入文件描述",
              width = "100%"
            )
          ),
          tags$div(
            class = "col-md-3",
            selectInput(
              paste0("single_sep_", file_id),
              "分隔符",
              choices = list(
                "自动检测" = "auto",
                "逗号 (,)" = ",",
                "制表符 (\\t)" = "\t",
                "分号 (;)" = ";",
                "空格" = " ",
                "竖线 (|)" = "|"
              ),
              selected = "auto",
              width = "100%"
            )
          ),
          tags$div(
            class = "col-md-1",
            tags$br(),
            actionButton(
              "remove_single_id",
              "",
              icon = icon("trash"),
              class = "btn-danger btn-sm",
              style = "margin-top: 5px;",
              onclick = paste0("Shiny.setInputValue('remove_single_id', ", file_id, ", {priority: 'event'});")
            )
          )
        )
      )
    )

    show_success(paste("已添加文件输入", file_id))
  })

  # 移除单个文件输入
  observeEvent(input$remove_single_id, {
    removeUI(selector = paste0("#single_file_input_", input$remove_single_id))

    # 从存储中移除对应文件
    file_key <- paste0("single_file_", input$remove_single_id)
    if (file_key %in% names(values$multi_files)) {
      values$multi_files[[file_key]] <- NULL
    }

    show_success("单个文件输入已移除")
  })

  # 清空所有文件
  observeEvent(input$clear_all_files, {
    # 移除所有动态创建的单个文件输入
    for (i in 1:values$file_counter) {
      removeUI(selector = paste0("#single_file_input_", i))
    }

    # 重置计数器和数据
    values$file_counter <- 0
    values$multi_files <- list()
    values$merged_data <- NULL
    values$merge_report <- NULL

    # 重置批量文件输入（通过JavaScript）
    tryCatch({
      shinyjs::reset("multi_files_batch")
    }, error = function(e) {
      # 如果shinyjs::reset失败，使用JavaScript直接重置
      session$sendCustomMessage("resetFileInput", "multi_files_batch")
    })

    show_success("所有文件已清空")
  })

  # 监听单个文件上传
  observe({
    for (i in 1:values$file_counter) {
      local({
        file_id <- paste0("single_file_", i)
        desc_id <- paste0("single_desc_", i)
        sep_id <- paste0("single_sep_", i)

        file_input <- input[[file_id]]
        file_desc <- input[[desc_id]]
        file_sep <- input[[sep_id]]

        if (!is.null(file_input)) {
          tryCatch({
            # 检测分隔符
            separator <- detect_separator(file_input$datapath, file_sep)

            # 读取文件
            data <- read_medical_data(
              file_input$datapath,
              separator = separator,
              encoding = input$batch_encoding,
              header = input$batch_header,
              stringsAsFactors = FALSE
            )

            # 存储文件信息
            values$multi_files[[file_id]] <- list(
              name = file_input$name,
              description = if (is.null(file_desc) || file_desc == "") paste("文件", i) else file_desc,
              data = data,
              upload_time = Sys.time(),
              separator = separator,
              encoding = input$batch_encoding
            )

            show_success(paste("文件", file_input$name, "上传成功"))

            # 更新合并键选择框
            update_merge_key_choices()

          }, error = function(e) {
            show_warning(paste("文件", file_input$name, "上传失败:", e$message))
          })
        }
      })
    }
  })

  # 文件状态表格输出（响应式）
  output$file_status_table <- DT::renderDataTable({
    if (length(values$multi_files) == 0) {
      data.frame(
        文件名 = "暂无文件",
        描述 = "",
        行数 = "",
        列数 = "",
        状态 = "等待上传",
        stringsAsFactors = FALSE
      )
    } else {
      status_data <- do.call(rbind, lapply(names(values$multi_files), function(key) {
        file_info <- values$multi_files[[key]]
        data.frame(
          文件名 = file_info$name,
          描述 = file_info$description,
          行数 = nrow(file_info$data),
          列数 = ncol(file_info$data),
          状态 = "已上传",
          stringsAsFactors = FALSE
        )
      }))
      status_data
    }
  }, options = list(dom = 't', pageLength = 10))

  # 文件合并处理
  observeEvent(input$merge_files, {
    req(length(values$multi_files) >= 2)

    tryCatch({
      showNotification("正在合并文件...", type = "message", duration = NULL, id = "merge_msg")

      # 参数验证和默认值设置
      merge_key <- input$merge_key
      if (is.null(merge_key) || length(merge_key) == 0 || merge_key == "") {
        merge_key <- "icustay_id"  # 设置默认值
        log_warn("merge_key参数为空，使用默认值: icustay_id")
      }
      
      merge_type <- input$merge_type
      if (is.null(merge_type) || length(merge_type) == 0 || merge_type == "") {
        merge_type <- "inner"  # 设置默认值
        log_warn("merge_type参数为空，使用默认值: inner")
      }

      # 执行文件合并
      merge_result <- merge_medical_files(
        file_list = values$multi_files,
        merge_key = merge_key,
        merge_type = merge_type,
        remove_duplicates = input$remove_duplicates,
        add_source_info = input$add_source
      )

      # 存储合并结果
      values$merged_data <- merge_result$data
      values$merge_report <- merge_result$report

      removeNotification("merge_msg")
      show_success(paste("文件合并完成！最终数据:", nrow(merge_result$data), "行,", ncol(merge_result$data), "列"))

      # 显示合并结果区域
      shinyjs::show("merge_results_section")

    }, error = function(e) {
      removeNotification("merge_msg")
      show_warning(paste("文件合并失败:", e$message))
    })
  })

  # 合并数据预览
  output$merged_data_preview <- DT::renderDataTable({
    req(values$merged_data)

    DT::datatable(
      values$merged_data,
      options = list(
        scrollX = TRUE,
        pageLength = 10,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      filter = "top",
      rownames = FALSE
    )
  })

  # 合并数据下载
  output$download_merged_data <- downloadHandler(
    filename = function() {
      paste0("merged_medical_data_", Sys.Date(), ".csv")
    },
    content = function(file) {
      req(values$merged_data)
      write.csv(values$merged_data, file, row.names = FALSE, fileEncoding = "UTF-8")
    }
  )

  # 合并报告内容
  output$merge_report_content <- renderUI({
    req(values$merge_report)

    report <- values$merge_report

    # 生成报告HTML
    tags$div(
      # 基本统计
      tags$div(
        class = "row",
        tags$div(
          class = "col-md-6",
          tags$div(
            style = "background: #d4edda; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
            tags$h5("合并统计", style = "margin: 0 0 10px 0; color: #155724;"),
            tags$p(paste("合并文件数:", report$summary$total_files)),
            tags$p(paste("合并类型:", report$summary$merge_type)),
            tags$p(paste("最终行数:", report$summary$final_rows)),
            tags$p(paste("最终列数:", report$summary$final_cols))
          )
        ),
        tags$div(
          class = "col-md-6",
          tags$div(
            style = "background: #cce5ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;",
            tags$h5("数据质量", style = "margin: 0 0 10px 0; color: #004085;"),
            tags$p(paste("缺失值比例:", paste0(round(report$quality$missing_percent, 2), "%"))),
            tags$p(paste("重复行数:", report$quality$duplicate_rows)),
            tags$p(paste("数据完整性:", paste0(round(report$quality$completeness, 2), "%")))
          )
        )
      ),

      # 文件详情
      tags$h5("文件详情", style = "color: #2c3e50; margin: 20px 0 15px 0;"),
      tags$div(
        do.call(tags$div, lapply(report$files_info, function(file_info) {
          tags$div(
            style = "border: 1px solid #ddd; border-radius: 5px; padding: 10px; margin-bottom: 10px;",
            tags$strong(file_info$name),
            tags$span(paste(" - ", file_info$description), style = "color: #666;"),
            tags$br(),
            tags$small(paste("行数:", file_info$rows, "| 列数:", file_info$cols))
          )
        }))
      )
    )
  })

  # 合并数据质量图表
  output$merged_data_quality <- renderPlot({
    req(values$merged_data)

    data <- values$merged_data

    # 创建缺失值分析图
    library(ggplot2)

    # 计算缺失值比例
    missing_data <- data.frame(
      Variable = names(data),
      Missing_Percent = sapply(data, function(x) sum(is.na(x)) / length(x) * 100),
      stringsAsFactors = FALSE
    )

    # 按缺失值比例排序
    missing_data <- missing_data[order(missing_data$Missing_Percent, decreasing = TRUE), ]
    missing_data$Variable <- factor(missing_data$Variable, levels = missing_data$Variable)

    # 创建图表
    ggplot(missing_data, aes(x = Variable, y = Missing_Percent)) +
      geom_bar(stat = "identity", fill = "#3498db", alpha = 0.7) +
      geom_text(aes(label = paste0(round(Missing_Percent, 1), "%")),
                vjust = -0.5, size = 3) +
      labs(
        title = "合并数据缺失值分析",
        x = "变量名",
        y = "缺失值比例 (%)"
      ) +
      theme_minimal() +
      theme(
        axis.text.x = element_text(angle = 45, hjust = 1),
        plot.title = element_text(hjust = 0.5, size = 14, face = "bold")
      ) +
      ylim(0, max(missing_data$Missing_Percent) * 1.1)
  })

  # 数据清洗功能（针对合并数据）
  observeEvent(input$clean_merged_data, {
    req(values$merged_data)

    tryCatch({
      showNotification("正在清洗合并数据...", type = "message", duration = NULL, id = "clean_merged_msg")

      # 准备清洗选项
      clean_options <- list(
        missing_method = input$merged_missing_method,
        mice_iterations = input$merged_mice_iterations,
        normalize = input$merged_normalize,
        remove_outliers = input$merged_remove_outliers,
        outlier_threshold = input$merged_outlier_threshold
      )

      # 执行数据清洗
      cleaned_data <- preprocess_medical_data(values$merged_data, clean_options)

      # 生成清洗报告
      cleaning_report <- generate_cleaning_report(values$merged_data, cleaned_data, clean_options)

      # 存储清洗后的数据
      values$cleaned_data <- cleaned_data
      values$cleaning_report <- cleaning_report

      # 更新主数据（用于后续分析）
      values$raw_data <- cleaned_data
      values$processed_data <- cleaned_data

      log_info("清洗后数据已传递到主数据存储，可用于统计分析和模型构建")

      # 触发数据更新通知
      showNotification(
        "数据已更新！现在可以进行统计分析和模型构建。",
        type = "message",
        duration = 5
      )

      removeNotification("clean_merged_msg")
      show_success("合并数据清洗完成！")

      # 显示清洗结果区域
      shinyjs::show("cleaning_results_section")

    }, error = function(e) {
      removeNotification("clean_merged_msg")
      show_warning(paste("合并数据清洗失败:", e$message))
    })
  })

  # 清洗后数据预览
  output$cleaned_data_preview <- DT::renderDataTable({
    req(values$cleaned_data)

    DT::datatable(
      values$cleaned_data,
      options = list(
        scrollX = TRUE,
        pageLength = 10,
        dom = 'Bfrtip',
        buttons = c('copy', 'csv', 'excel')
      ),
      class = "display nowrap compact",
      filter = "top",
      rownames = FALSE
    )
  })

  # 清洗后数据下载
  output$download_cleaned_data <- downloadHandler(
    filename = function() {
      paste0("cleaned_medical_data_", Sys.Date(), ".csv")
    },
    content = function(file) {
      req(values$cleaned_data)
      write.csv(values$cleaned_data, file, row.names = FALSE, fileEncoding = "UTF-8")
    }
  )
}
